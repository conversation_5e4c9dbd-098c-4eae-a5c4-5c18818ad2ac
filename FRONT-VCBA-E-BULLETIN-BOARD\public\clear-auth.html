<!DOCTYPE html>
<html>
<head>
    <title>Clear Authentication</title>
</head>
<body>
    <h1>Authentication Debug Tool</h1>
    <button onclick="clearAuth()">Clear All Auth Tokens</button>
    <button onclick="checkAuth()">Check Current Auth State</button>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        function clearAuth() {
            // Clear all possible auth tokens
            const keysToRemove = [
                'vcba_admin_auth_token',
                'vcba_admin_refresh_token', 
                'vcba_admin_user_data',
                'accessToken',
                'refreshToken',
                'authToken',
                'token'
            ];

            console.log('🔧 Clearing authentication tokens...');
            console.log('📊 Current localStorage keys:', Object.keys(localStorage));

            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    console.log(`🗑️ Removing: ${key}`);
                    localStorage.removeItem(key);
                } else {
                    console.log(`⚪ Not found: ${key}`);
                }
            });

            console.log('✅ Auth tokens cleared');
            console.log('📊 Remaining localStorage keys:', Object.keys(localStorage));
            
            document.getElementById('result').innerHTML = `
                <p style="color: green;">✅ Authentication tokens cleared!</p>
                <p>You can now <a href="/admin/login">login again</a></p>
            `;
        }

        function checkAuth() {
            console.log('🔍 Checking authentication state...');
            const token = localStorage.getItem('vcba_admin_auth_token');
            const userData = localStorage.getItem('vcba_admin_user_data');
            const refreshToken = localStorage.getItem('vcba_admin_refresh_token');

            document.getElementById('result').innerHTML = `
                <h3>Current Auth State:</h3>
                <p><strong>Access Token:</strong> ${token ? '✅ Present (' + token.substring(0, 20) + '...)' : '❌ Missing'}</p>
                <p><strong>Refresh Token:</strong> ${refreshToken ? '✅ Present' : '❌ Missing'}</p>
                <p><strong>User Data:</strong> ${userData ? '✅ Present' : '❌ Missing'}</p>
                ${userData ? '<pre>' + JSON.stringify(JSON.parse(userData), null, 2) + '</pre>' : ''}
            `;
        }

        function testLogin() {
            document.getElementById('result').innerHTML = `
                <h3>Test Login</h3>
                <form onsubmit="doLogin(event)">
                    <p><input type="email" id="email" placeholder="Email" value="<EMAIL>" required></p>
                    <p><input type="password" id="password" placeholder="Password" required></p>
                    <p><button type="submit">Login</button></p>
                </form>
                <div id="loginResult"></div>
            `;
        }

        async function doLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        userType: 'admin'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Store tokens
                    localStorage.setItem('vcba_admin_auth_token', data.data.accessToken);
                    localStorage.setItem('vcba_admin_refresh_token', data.data.refreshToken);
                    localStorage.setItem('vcba_admin_user_data', JSON.stringify(data.data.user));

                    document.getElementById('loginResult').innerHTML = `
                        <p style="color: green;">✅ Login successful!</p>
                        <p>User: ${data.data.user.firstName} ${data.data.user.lastName}</p>
                        <p>Position: ${data.data.user.position}</p>
                        <p>You can now access <a href="/admin/categories">Categories</a> and <a href="/admin/admin-management">Admin Management</a></p>
                    `;
                } else {
                    document.getElementById('loginResult').innerHTML = `
                        <p style="color: red;">❌ Login failed: ${data.message}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = `
                    <p style="color: red;">❌ Login error: ${error.message}</p>
                `;
            }
        }

        // Auto-run on page load
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
