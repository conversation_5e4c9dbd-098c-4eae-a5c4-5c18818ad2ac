[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx": "68", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "69", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx": "70", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx": "71", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx": "72", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts": "73", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx": "74", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx": "75", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx": "76", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\tv\\TVDisplay.tsx": "77", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVSlideshow.tsx": "78", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVAnnouncement.tsx": "79", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVCalendarEvent.tsx": "80", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVControlPanel.tsx": "81", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\tvControlService.ts": "82", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVContentManager.tsx": "83", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVPlaybackControls.tsx": "84", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVStatusMonitor.tsx": "85", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVEmergencyBroadcast.tsx": "86", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVDisplaySettings.tsx": "87", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\permissions.ts": "88", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AuthContext.tsx": "89", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminManagement.tsx": "90", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\CategoryManagement.tsx": "91", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\SMSSettings.tsx": "92", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AuditLogs.tsx": "93", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\BulkOperations.tsx": "94", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountList.tsx": "95", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountModal.tsx": "96", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryList.tsx": "97", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryModal.tsx": "98", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\categoryService.ts": "99", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.ts": "100", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\adminManagementService.ts": "101"}, {"size": 554, "mtime": *************, "results": "102", "hashOfConfig": "103"}, {"size": 419, "mtime": *************, "results": "104", "hashOfConfig": "103"}, {"size": 8134, "mtime": *************, "results": "105", "hashOfConfig": "103"}, {"size": 7830, "mtime": *************, "results": "106", "hashOfConfig": "103"}, {"size": 3769, "mtime": *************, "results": "107", "hashOfConfig": "103"}, {"size": 6136, "mtime": *************, "results": "108", "hashOfConfig": "103"}, {"size": 7447, "mtime": 1752271830000, "results": "109", "hashOfConfig": "103"}, {"size": 21118, "mtime": 1754414563128, "results": "110", "hashOfConfig": "103"}, {"size": 50360, "mtime": 1753139353321, "results": "111", "hashOfConfig": "103"}, {"size": 45894, "mtime": 1752867480062, "results": "112", "hashOfConfig": "103"}, {"size": 5757, "mtime": 1752391390000, "results": "113", "hashOfConfig": "103"}, {"size": 81742, "mtime": 1754410578323, "results": "114", "hashOfConfig": "103"}, {"size": 56, "mtime": 1753607885462, "results": "115", "hashOfConfig": "103"}, {"size": 1342, "mtime": 1751155290000, "results": "116", "hashOfConfig": "103"}, {"size": 1688, "mtime": 1751208620000, "results": "117", "hashOfConfig": "103"}, {"size": 103, "mtime": 1751140878000, "results": "118", "hashOfConfig": "103"}, {"size": 232, "mtime": 1751541102000, "results": "119", "hashOfConfig": "103"}, {"size": 3996, "mtime": 1751807708000, "results": "120", "hashOfConfig": "103"}, {"size": 8153, "mtime": 1753729692309, "results": "121", "hashOfConfig": "103"}, {"size": 16125, "mtime": 1754418855469, "results": "122", "hashOfConfig": "103"}, {"size": 17132, "mtime": *************, "results": "123", "hashOfConfig": "103"}, {"size": 7759, "mtime": 1753726439666, "results": "124", "hashOfConfig": "103"}, {"size": 14456, "mtime": 1753728961926, "results": "125", "hashOfConfig": "103"}, {"size": 8587, "mtime": 1752337674000, "results": "126", "hashOfConfig": "103"}, {"size": 25200, "mtime": 1753729065843, "results": "127", "hashOfConfig": "103"}, {"size": 12906, "mtime": 1752333634000, "results": "128", "hashOfConfig": "103"}, {"size": 6264, "mtime": 1753735348071, "results": "129", "hashOfConfig": "103"}, {"size": 9834, "mtime": 1753077700688, "results": "130", "hashOfConfig": "103"}, {"size": 8214, "mtime": 1752334762000, "results": "131", "hashOfConfig": "103"}, {"size": 12982, "mtime": 1752333550000, "results": "132", "hashOfConfig": "103"}, {"size": 19475, "mtime": 1753729131110, "results": "133", "hashOfConfig": "103"}, {"size": 15593, "mtime": 1752760674000, "results": "134", "hashOfConfig": "103"}, {"size": 12325, "mtime": 1752330204000, "results": "135", "hashOfConfig": "103"}, {"size": 28178, "mtime": 1753079518460, "results": "136", "hashOfConfig": "103"}, {"size": 24168, "mtime": 1752867418410, "results": "137", "hashOfConfig": "103"}, {"size": 39901, "mtime": 1753141345895, "results": "138", "hashOfConfig": "103"}, {"size": 11323, "mtime": 1754418148661, "results": "139", "hashOfConfig": "103"}, {"size": 9059, "mtime": 1753730582696, "results": "140", "hashOfConfig": "103"}, {"size": 17223, "mtime": 1754414290097, "results": "141", "hashOfConfig": "103"}, {"size": 5633, "mtime": 1753730655191, "results": "142", "hashOfConfig": "103"}, {"size": 2063, "mtime": 1751140856000, "results": "143", "hashOfConfig": "103"}, {"size": 2236, "mtime": 1753600593548, "results": "144", "hashOfConfig": "103"}, {"size": 4237, "mtime": 1754418337770, "results": "145", "hashOfConfig": "103"}, {"size": 230, "mtime": 1751371668000, "results": "146", "hashOfConfig": "103"}, {"size": 10813, "mtime": 1752761372000, "results": "147", "hashOfConfig": "103"}, {"size": 14506, "mtime": 1752879590759, "results": "148", "hashOfConfig": "103"}, {"size": 26448, "mtime": 1752380598000, "results": "149", "hashOfConfig": "103"}, {"size": 10147, "mtime": 1752334796000, "results": "150", "hashOfConfig": "103"}, {"size": 10510, "mtime": 1752310980000, "results": "151", "hashOfConfig": "103"}, {"size": 10877, "mtime": 1752092600000, "results": "152", "hashOfConfig": "103"}, {"size": 7318, "mtime": 1752381124000, "results": "153", "hashOfConfig": "103"}, {"size": 5263, "mtime": 1752867135753, "results": "154", "hashOfConfig": "103"}, {"size": 19520, "mtime": 1752338090000, "results": "155", "hashOfConfig": "103"}, {"size": 13444, "mtime": 1752870507174, "results": "156", "hashOfConfig": "103"}, {"size": 16870, "mtime": 1752338106000, "results": "157", "hashOfConfig": "103"}, {"size": 616, "mtime": 1752865556056, "results": "158", "hashOfConfig": "103"}, {"size": 15760, "mtime": 1752828048867, "results": "159", "hashOfConfig": "103"}, {"size": 9958, "mtime": 1751375132000, "results": "160", "hashOfConfig": "103"}, {"size": 42, "mtime": 1751129052000, "results": "161", "hashOfConfig": "103"}, {"size": 20869, "mtime": 1752885135797, "results": "162", "hashOfConfig": "103"}, {"size": 10085, "mtime": 1753141914993, "results": "163", "hashOfConfig": "103"}, {"size": 9297, "mtime": 1751476824000, "results": "164", "hashOfConfig": "103"}, {"size": 4237, "mtime": 1753722668541, "results": "165", "hashOfConfig": "103"}, {"size": 3686, "mtime": 1752890875441, "results": "166", "hashOfConfig": "103"}, {"size": 18927, "mtime": 1753141219821, "results": "167", "hashOfConfig": "103"}, {"size": 7423, "mtime": 1753077217399, "results": "168", "hashOfConfig": "103"}, {"size": 18379, "mtime": 1753155927801, "results": "169", "hashOfConfig": "103"}, {"size": 18988, "mtime": 1753609851189, "results": "170", "hashOfConfig": "103"}, {"size": 90955, "mtime": 1753616469959, "results": "171", "hashOfConfig": "103"}, {"size": 104542, "mtime": 1753730873531, "results": "172", "hashOfConfig": "103"}, {"size": 30091, "mtime": 1753732118317, "results": "173", "hashOfConfig": "103"}, {"size": 9960, "mtime": 1753735972583, "results": "174", "hashOfConfig": "103"}, {"size": 6463, "mtime": 1753736254532, "results": "175", "hashOfConfig": "103"}, {"size": 16384, "mtime": 1753735939665, "results": "176", "hashOfConfig": "103"}, {"size": 17240, "mtime": 1753735956350, "results": "177", "hashOfConfig": "103"}, {"size": 16677, "mtime": 1753735924469, "results": "178", "hashOfConfig": "103"}, {"size": 14569, "mtime": 1754018974966, "results": "179", "hashOfConfig": "103"}, {"size": 7457, "mtime": 1754019701685, "results": "180", "hashOfConfig": "103"}, {"size": 11910, "mtime": 1754020518834, "results": "181", "hashOfConfig": "103"}, {"size": 16995, "mtime": 1754020537961, "results": "182", "hashOfConfig": "103"}, {"size": 6637, "mtime": 1753998161360, "results": "183", "hashOfConfig": "103"}, {"size": 8154, "mtime": 1754018255515, "results": "184", "hashOfConfig": "103"}, {"size": 11027, "mtime": 1753997495633, "results": "185", "hashOfConfig": "103"}, {"size": 8928, "mtime": 1753997414781, "results": "186", "hashOfConfig": "103"}, {"size": 12092, "mtime": 1753997586877, "results": "187", "hashOfConfig": "103"}, {"size": 9584, "mtime": 1754018489138, "results": "188", "hashOfConfig": "103"}, {"size": 9550, "mtime": 1753997452188, "results": "189", "hashOfConfig": "103"}, {"size": 8618, "mtime": 1754410542102, "results": "190", "hashOfConfig": "103"}, {"size": 8587, "mtime": 1751374598000, "results": "191", "hashOfConfig": "103"}, {"size": 15961, "mtime": 1754418855470, "results": "192", "hashOfConfig": "103"}, {"size": 22084, "mtime": 1754418855586, "results": "193", "hashOfConfig": "103"}, {"size": 18260, "mtime": 1754396565757, "results": "194", "hashOfConfig": "103"}, {"size": 22101, "mtime": 1754411925693, "results": "195", "hashOfConfig": "103"}, {"size": 21184, "mtime": 1754411548324, "results": "196", "hashOfConfig": "103"}, {"size": 14612, "mtime": 1754414659592, "results": "197", "hashOfConfig": "103"}, {"size": 21889, "mtime": 1754418855472, "results": "198", "hashOfConfig": "103"}, {"size": 20601, "mtime": 1754415486074, "results": "199", "hashOfConfig": "103"}, {"size": 16611, "mtime": 1754415500851, "results": "200", "hashOfConfig": "103"}, {"size": 8969, "mtime": 1754418855469, "results": "201", "hashOfConfig": "103"}, {"size": 960, "mtime": 1754414011607, "results": "202", "hashOfConfig": "103"}, {"size": 7777, "mtime": 1754418855472, "results": "203", "hashOfConfig": "103"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["507", "508"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["509"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", ["510", "511", "512", "513"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["514", "515", "516", "517"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["518", "519", "520"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["521", "522", "523"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["524"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["525"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["526"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["527"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["528"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["529", "530", "531"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["532"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["533"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["534", "535", "536", "537", "538"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["539", "540", "541", "542"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["543"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["544", "545", "546", "547"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", ["548", "549"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["550", "551", "552", "553", "554"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["555", "556", "557", "558", "559"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["560", "561", "562", "563", "564", "565"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["566", "567", "568"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["569"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["570", "571"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["572"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["573", "574", "575"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["576", "577", "578"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["579"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["580", "581", "582"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx", ["583", "584", "585"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts", ["586"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["587", "588", "589", "590", "591", "592"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx", ["593"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx", ["594", "595", "596", "597"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx", ["598"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx", ["599"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx", ["600"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\tv\\TVDisplay.tsx", ["601", "602"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVSlideshow.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVAnnouncement.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVCalendarEvent.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVControlPanel.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\tvControlService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVContentManager.tsx", ["603", "604", "605"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVPlaybackControls.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVStatusMonitor.tsx", ["606"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVEmergencyBroadcast.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVDisplaySettings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\permissions.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AuthContext.tsx", ["607"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminManagement.tsx", ["608", "609", "610"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\CategoryManagement.tsx", ["611"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\SMSSettings.tsx", ["612", "613"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AuditLogs.tsx", ["614", "615"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\BulkOperations.tsx", ["616"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountList.tsx", ["617", "618", "619", "620"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryList.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryModal.tsx", ["621"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\categoryService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\adminManagementService.ts", [], [], {"ruleId": "622", "severity": 1, "message": "623", "line": 3, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 22}, {"ruleId": "622", "severity": 1, "message": "626", "line": 26, "column": 8, "nodeType": "624", "messageId": "625", "endLine": 26, "endColumn": 21}, {"ruleId": "622", "severity": 1, "message": "627", "line": 2, "column": 28, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 38}, {"ruleId": "622", "severity": 1, "message": "628", "line": 4, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 4, "endColumn": 14}, {"ruleId": "622", "severity": 1, "message": "629", "line": 4, "column": 28, "nodeType": "624", "messageId": "625", "endLine": 4, "endColumn": 40}, {"ruleId": "622", "severity": 1, "message": "630", "line": 4, "column": 48, "nodeType": "624", "messageId": "625", "endLine": 4, "endColumn": 52}, {"ruleId": "622", "severity": 1, "message": "631", "line": 10, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 10, "endColumn": 20}, {"ruleId": "622", "severity": 1, "message": "632", "line": 6, "column": 132, "nodeType": "624", "messageId": "625", "endLine": 6, "endColumn": 137}, {"ruleId": "633", "severity": 1, "message": "634", "line": 217, "column": 6, "nodeType": "635", "endLine": 217, "endColumn": 57, "suggestions": "636"}, {"ruleId": "633", "severity": 1, "message": "637", "line": 217, "column": 7, "nodeType": "638", "endLine": 217, "endColumn": 32}, {"ruleId": "633", "severity": 1, "message": "637", "line": 217, "column": 34, "nodeType": "638", "endLine": 217, "endColumn": 56}, {"ruleId": "622", "severity": 1, "message": "639", "line": 53, "column": 19, "nodeType": "624", "messageId": "625", "endLine": 53, "endColumn": 29}, {"ruleId": "622", "severity": 1, "message": "640", "line": 83, "column": 5, "nodeType": "624", "messageId": "625", "endLine": 83, "endColumn": 10}, {"ruleId": "633", "severity": 1, "message": "641", "line": 162, "column": 6, "nodeType": "635", "endLine": 162, "endColumn": 82, "suggestions": "642"}, {"ruleId": "622", "severity": 1, "message": "643", "line": 2, "column": 57, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 73}, {"ruleId": "622", "severity": 1, "message": "644", "line": 20, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 20, "endColumn": 23}, {"ruleId": "633", "severity": 1, "message": "645", "line": 121, "column": 6, "nodeType": "635", "endLine": 121, "endColumn": 60, "suggestions": "646"}, {"ruleId": "633", "severity": 1, "message": "647", "line": 39, "column": 6, "nodeType": "635", "endLine": 39, "endColumn": 16, "suggestions": "648"}, {"ruleId": "622", "severity": 1, "message": "649", "line": 35, "column": 32, "nodeType": "624", "messageId": "625", "endLine": 35, "endColumn": 33}, {"ruleId": "622", "severity": 1, "message": "650", "line": 3, "column": 25, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 37}, {"ruleId": "633", "severity": 1, "message": "651", "line": 64, "column": 6, "nodeType": "635", "endLine": 64, "endColumn": 17, "suggestions": "652"}, {"ruleId": "622", "severity": 1, "message": "653", "line": 93, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 93, "endColumn": 19}, {"ruleId": "622", "severity": 1, "message": "654", "line": 7, "column": 3, "nodeType": "624", "messageId": "625", "endLine": 7, "endColumn": 15}, {"ruleId": "633", "severity": 1, "message": "655", "line": 82, "column": 6, "nodeType": "635", "endLine": 82, "endColumn": 33, "suggestions": "656"}, {"ruleId": "633", "severity": 1, "message": "657", "line": 82, "column": 7, "nodeType": "638", "endLine": 82, "endColumn": 32}, {"ruleId": "633", "severity": 1, "message": "658", "line": 57, "column": 6, "nodeType": "635", "endLine": 57, "endColumn": 16, "suggestions": "659"}, {"ruleId": "622", "severity": 1, "message": "653", "line": 93, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 93, "endColumn": 19}, {"ruleId": "622", "severity": 1, "message": "660", "line": 2, "column": 31, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 55}, {"ruleId": "622", "severity": 1, "message": "661", "line": 8, "column": 15, "nodeType": "624", "messageId": "625", "endLine": 8, "endColumn": 26}, {"ruleId": "633", "severity": 1, "message": "662", "line": 121, "column": 6, "nodeType": "635", "endLine": 121, "endColumn": 105, "suggestions": "663"}, {"ruleId": "633", "severity": 1, "message": "657", "line": 121, "column": 7, "nodeType": "638", "endLine": 121, "endColumn": 30}, {"ruleId": "633", "severity": 1, "message": "664", "line": 324, "column": 6, "nodeType": "635", "endLine": 324, "endColumn": 48, "suggestions": "665"}, {"ruleId": "622", "severity": 1, "message": "666", "line": 3, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 21}, {"ruleId": "633", "severity": 1, "message": "651", "line": 56, "column": 6, "nodeType": "635", "endLine": 56, "endColumn": 17, "suggestions": "667"}, {"ruleId": "622", "severity": 1, "message": "668", "line": 147, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 147, "endColumn": 21}, {"ruleId": "633", "severity": 1, "message": "669", "line": 180, "column": 6, "nodeType": "635", "endLine": 180, "endColumn": 23, "suggestions": "670"}, {"ruleId": "633", "severity": 1, "message": "671", "line": 182, "column": 6, "nodeType": "635", "endLine": 182, "endColumn": 113, "suggestions": "672"}, {"ruleId": "622", "severity": 1, "message": "673", "line": 2, "column": 34, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 37}, {"ruleId": "633", "severity": 1, "message": "651", "line": 65, "column": 6, "nodeType": "635", "endLine": 65, "endColumn": 17, "suggestions": "674"}, {"ruleId": "675", "severity": 1, "message": "676", "line": 248, "column": 11, "nodeType": "677", "endLine": 262, "endColumn": 13}, {"ruleId": "675", "severity": 1, "message": "676", "line": 332, "column": 19, "nodeType": "677", "endLine": 346, "endColumn": 21}, {"ruleId": "622", "severity": 1, "message": "628", "line": 6, "column": 73, "nodeType": "624", "messageId": "625", "endLine": 6, "endColumn": 77}, {"ruleId": "622", "severity": 1, "message": "678", "line": 16, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 16, "endColumn": 17}, {"ruleId": "622", "severity": 1, "message": "679", "line": 2, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 24}, {"ruleId": "622", "severity": 1, "message": "680", "line": 12, "column": 3, "nodeType": "624", "messageId": "625", "endLine": 12, "endColumn": 17}, {"ruleId": "622", "severity": 1, "message": "681", "line": 13, "column": 3, "nodeType": "624", "messageId": "625", "endLine": 13, "endColumn": 28}, {"ruleId": "633", "severity": 1, "message": "682", "line": 140, "column": 6, "nodeType": "635", "endLine": 140, "endColumn": 57, "suggestions": "683"}, {"ruleId": "684", "severity": 1, "message": "685", "line": 426, "column": 44, "nodeType": "686", "messageId": "687", "endLine": 426, "endColumn": 98}, {"ruleId": "622", "severity": 1, "message": "688", "line": 1, "column": 22, "nodeType": "624", "messageId": "625", "endLine": 1, "endColumn": 37}, {"ruleId": "622", "severity": 1, "message": "689", "line": 1, "column": 39, "nodeType": "624", "messageId": "625", "endLine": 1, "endColumn": 56}, {"ruleId": "622", "severity": 1, "message": "690", "line": 2, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 26}, {"ruleId": "691", "severity": 1, "message": "692", "line": 581, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 583, "endColumn": 4}, {"ruleId": "691", "severity": 1, "message": "692", "line": 647, "column": 3, "nodeType": "693", "messageId": "694", "endLine": 649, "endColumn": 4}, {"ruleId": "633", "severity": 1, "message": "695", "line": 128, "column": 6, "nodeType": "635", "endLine": 128, "endColumn": 18, "suggestions": "696"}, {"ruleId": "633", "severity": 1, "message": "697", "line": 173, "column": 6, "nodeType": "635", "endLine": 173, "endColumn": 33, "suggestions": "698"}, {"ruleId": "633", "severity": 1, "message": "697", "line": 204, "column": 6, "nodeType": "635", "endLine": 204, "endColumn": 21, "suggestions": "699"}, {"ruleId": "633", "severity": 1, "message": "697", "line": 239, "column": 6, "nodeType": "635", "endLine": 239, "endColumn": 33, "suggestions": "700"}, {"ruleId": "633", "severity": 1, "message": "701", "line": 299, "column": 6, "nodeType": "635", "endLine": 299, "endColumn": 22, "suggestions": "702"}, {"ruleId": "633", "severity": 1, "message": "703", "line": 306, "column": 6, "nodeType": "635", "endLine": 306, "endColumn": 18, "suggestions": "704"}, {"ruleId": "684", "severity": 1, "message": "685", "line": 35, "column": 44, "nodeType": "686", "messageId": "687", "endLine": 35, "endColumn": 98}, {"ruleId": "684", "severity": 1, "message": "705", "line": 96, "column": 46, "nodeType": "686", "messageId": "687", "endLine": 96, "endColumn": 97}, {"ruleId": "706", "severity": 1, "message": "707", "line": 234, "column": 1, "nodeType": "708", "endLine": 248, "endColumn": 3}, {"ruleId": "622", "severity": 1, "message": "709", "line": 23, "column": 11, "nodeType": "624", "messageId": "625", "endLine": 23, "endColumn": 28}, {"ruleId": "633", "severity": 1, "message": "651", "line": 100, "column": 6, "nodeType": "635", "endLine": 100, "endColumn": 17, "suggestions": "710"}, {"ruleId": "633", "severity": 1, "message": "711", "line": 375, "column": 6, "nodeType": "635", "endLine": 375, "endColumn": 8, "suggestions": "712"}, {"ruleId": "713", "severity": 1, "message": "714", "line": 259, "column": 7, "nodeType": "677", "endLine": 273, "endColumn": 8}, {"ruleId": "633", "severity": 1, "message": "651", "line": 66, "column": 6, "nodeType": "635", "endLine": 66, "endColumn": 17, "suggestions": "715"}, {"ruleId": "622", "severity": 1, "message": "716", "line": 385, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 385, "endColumn": 24}, {"ruleId": "633", "severity": 1, "message": "717", "line": 404, "column": 6, "nodeType": "635", "endLine": 404, "endColumn": 8, "suggestions": "718"}, {"ruleId": "622", "severity": 1, "message": "719", "line": 2, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 14}, {"ruleId": "622", "severity": 1, "message": "720", "line": 90, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 90, "endColumn": 33}, {"ruleId": "622", "severity": 1, "message": "721", "line": 94, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 94, "endColumn": 26}, {"ruleId": "622", "severity": 1, "message": "722", "line": 4, "column": 27, "nodeType": "624", "messageId": "625", "endLine": 4, "endColumn": 39}, {"ruleId": "622", "severity": 1, "message": "719", "line": 2, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 14}, {"ruleId": "622", "severity": 1, "message": "720", "line": 105, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 105, "endColumn": 33}, {"ruleId": "622", "severity": 1, "message": "723", "line": 110, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 110, "endColumn": 16}, {"ruleId": "622", "severity": 1, "message": "724", "line": 2, "column": 49, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 60}, {"ruleId": "622", "severity": 1, "message": "673", "line": 3, "column": 57, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 60}, {"ruleId": "633", "severity": 1, "message": "725", "line": 40, "column": 6, "nodeType": "635", "endLine": 40, "endColumn": 19, "suggestions": "726"}, {"ruleId": "622", "severity": 1, "message": "727", "line": 2, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 21}, {"ruleId": "622", "severity": 1, "message": "728", "line": 3, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 29}, {"ruleId": "622", "severity": 1, "message": "729", "line": 23, "column": 3, "nodeType": "624", "messageId": "625", "endLine": 23, "endColumn": 7}, {"ruleId": "633", "severity": 1, "message": "651", "line": 101, "column": 6, "nodeType": "635", "endLine": 101, "endColumn": 17, "suggestions": "730"}, {"ruleId": "622", "severity": 1, "message": "731", "line": 473, "column": 31, "nodeType": "624", "messageId": "625", "endLine": 473, "endColumn": 45}, {"ruleId": "622", "severity": 1, "message": "732", "line": 536, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 536, "endColumn": 28}, {"ruleId": "622", "severity": 1, "message": "733", "line": 702, "column": 9, "nodeType": "624", "messageId": "625", "endLine": 702, "endColumn": 24}, {"ruleId": "622", "severity": 1, "message": "734", "line": 4, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 4, "endColumn": 33}, {"ruleId": "622", "severity": 1, "message": "735", "line": 2, "column": 61, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 70}, {"ruleId": "622", "severity": 1, "message": "736", "line": 2, "column": 72, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 78}, {"ruleId": "622", "severity": 1, "message": "737", "line": 2, "column": 80, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 86}, {"ruleId": "622", "severity": 1, "message": "738", "line": 2, "column": 88, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 94}, {"ruleId": "633", "severity": 1, "message": "739", "line": 23, "column": 6, "nodeType": "635", "endLine": 23, "endColumn": 31, "suggestions": "740"}, {"ruleId": "633", "severity": 1, "message": "741", "line": 23, "column": 6, "nodeType": "635", "endLine": 23, "endColumn": 31, "suggestions": "742"}, {"ruleId": "633", "severity": 1, "message": "743", "line": 24, "column": 6, "nodeType": "635", "endLine": 24, "endColumn": 31, "suggestions": "744"}, {"ruleId": "633", "severity": 1, "message": "745", "line": 148, "column": 6, "nodeType": "635", "endLine": 148, "endColumn": 31, "suggestions": "746"}, {"ruleId": "633", "severity": 1, "message": "747", "line": 164, "column": 6, "nodeType": "635", "endLine": 164, "endColumn": 8, "suggestions": "748"}, {"ruleId": "622", "severity": 1, "message": "749", "line": 1, "column": 27, "nodeType": "624", "messageId": "625", "endLine": 1, "endColumn": 36}, {"ruleId": "622", "severity": 1, "message": "750", "line": 18, "column": 14, "nodeType": "624", "messageId": "625", "endLine": 18, "endColumn": 34}, {"ruleId": "622", "severity": 1, "message": "751", "line": 30, "column": 14, "nodeType": "624", "messageId": "625", "endLine": 30, "endColumn": 27}, {"ruleId": "622", "severity": 1, "message": "752", "line": 3, "column": 52, "nodeType": "624", "messageId": "625", "endLine": 3, "endColumn": 61}, {"ruleId": "622", "severity": 1, "message": "753", "line": 167, "column": 13, "nodeType": "624", "messageId": "625", "endLine": 167, "endColumn": 25}, {"ruleId": "622", "severity": 1, "message": "738", "line": 2, "column": 28, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 34}, {"ruleId": "622", "severity": 1, "message": "754", "line": 5, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 5, "endColumn": 32}, {"ruleId": "633", "severity": 1, "message": "755", "line": 64, "column": 6, "nodeType": "635", "endLine": 64, "endColumn": 104, "suggestions": "756"}, {"ruleId": "633", "severity": 1, "message": "757", "line": 67, "column": 6, "nodeType": "635", "endLine": 67, "endColumn": 39, "suggestions": "758"}, {"ruleId": "622", "severity": 1, "message": "759", "line": 42, "column": 10, "nodeType": "624", "messageId": "625", "endLine": 42, "endColumn": 19}, {"ruleId": "622", "severity": 1, "message": "760", "line": 42, "column": 21, "nodeType": "624", "messageId": "625", "endLine": 42, "endColumn": 33}, {"ruleId": "622", "severity": 1, "message": "737", "line": 2, "column": 18, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 24}, {"ruleId": "622", "severity": 1, "message": "761", "line": 32, "column": 24, "nodeType": "624", "messageId": "625", "endLine": 32, "endColumn": 39}, {"ruleId": "622", "severity": 1, "message": "762", "line": 2, "column": 45, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 53}, {"ruleId": "622", "severity": 1, "message": "763", "line": 2, "column": 43, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 47}, {"ruleId": "622", "severity": 1, "message": "764", "line": 2, "column": 49, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 54}, {"ruleId": "622", "severity": 1, "message": "762", "line": 2, "column": 56, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 64}, {"ruleId": "622", "severity": 1, "message": "765", "line": 2, "column": 66, "nodeType": "624", "messageId": "625", "endLine": 2, "endColumn": 72}, {"ruleId": "633", "severity": 1, "message": "766", "line": 86, "column": 6, "nodeType": "635", "endLine": 86, "endColumn": 43, "suggestions": "767"}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'StudentLayout' is defined but never used.", "'ToastProps' is defined but never used.", "'User' is defined but never used.", "'SettingsIcon' is defined but never used.", "'Bell' is defined but never used.", "'permissions' is assigned a value but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["768"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["769"], "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'permissions.isSuperAdmin'. Either include it or remove the dependency array.", ["770"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["771"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["772"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useCallback has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", ["773"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["774"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["775"], "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["776"], "'getImageUrl' is defined but never used.", ["777"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["778"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["779"], "'Eye' is defined but never used.", ["780"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'navigate' is assigned a value but never used.", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["781"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["782"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["783"], ["784"], ["785"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["786"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["787"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["788"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["789"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["790"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["791"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", "'SyncResults' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["792"], "'ApiResponse' is defined but never used.", "'announcementService' is defined but never used.", "'Edit' is defined but never used.", ["793"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'calendarReactionService' is defined but never used.", "'RotateCcw' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Filter' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEvents'. Either include it or remove the dependency array.", ["794"], "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["795"], "React Hook useEffect has a missing dependency: 'loadAnnouncements'. Either include it or remove the dependency array.", ["796"], "React Hook useEffect has a missing dependency: 'createSlideContent'. Either include it or remove the dependency array.", ["797"], "React Hook useEffect has a missing dependency: 'handleControlCommand'. Either include it or remove the dependency array.", ["798"], "'useEffect' is defined but never used.", "'announcementsLoading' is assigned a value but never used.", "'eventsLoading' is assigned a value but never used.", "'RefreshCw' is defined but never used.", "'redirectPath' is assigned a value but never used.", "'adminManagementService' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdmins'. Either include it or remove the dependency array.", ["799"], "React Hook useEffect has missing dependencies: 'permissions' and 'user'. Either include them or remove the dependency array.", ["800"], "'templates' is assigned a value but never used.", "'setTemplates' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'Calendar' is defined but never used.", "'Mail' is defined but never used.", "'Phone' is defined but never used.", "'Shield' is defined but never used.", "React Hook useEffect has missing dependencies: 'isEditMode' and 'isSubcategoryMode'. Either include them or remove the dependency array.", ["801"], {"desc": "802", "fix": "803"}, {"desc": "804", "fix": "805"}, {"desc": "806", "fix": "807"}, {"desc": "808", "fix": "809"}, {"desc": "810", "fix": "811"}, {"desc": "802", "fix": "812"}, {"desc": "813", "fix": "814"}, {"desc": "815", "fix": "816"}, {"desc": "817", "fix": "818"}, {"desc": "810", "fix": "819"}, {"desc": "820", "fix": "821"}, {"desc": "822", "fix": "823"}, {"desc": "810", "fix": "824"}, {"desc": "825", "fix": "826"}, {"desc": "827", "fix": "828"}, {"desc": "829", "fix": "830"}, {"desc": "831", "fix": "832"}, {"desc": "829", "fix": "833"}, {"desc": "834", "fix": "835"}, {"desc": "836", "fix": "837"}, {"desc": "810", "fix": "838"}, {"desc": "839", "fix": "840"}, {"desc": "810", "fix": "841"}, {"desc": "839", "fix": "842"}, {"desc": "843", "fix": "844"}, {"desc": "810", "fix": "845"}, {"desc": "846", "fix": "847"}, {"desc": "848", "fix": "849"}, {"desc": "850", "fix": "851"}, {"desc": "852", "fix": "853"}, {"desc": "854", "fix": "855"}, {"desc": "856", "fix": "857"}, {"desc": "858", "fix": "859"}, {"desc": "860", "fix": "861"}, "Update the dependencies array to be: [currentDate]", {"range": "862", "text": "863"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "864", "text": "865"}, "Update the dependencies array to be: [currentPage, debouncedSearchTerm, filterStatus, permissions.isSuperAdmin, user.grade_level]", {"range": "866", "text": "867"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "868", "text": "869"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "870", "text": "871"}, {"range": "872", "text": "863"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "873", "text": "874"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "875", "text": "876"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "877", "text": "878"}, {"range": "879", "text": "871"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "880", "text": "881"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "882", "text": "883"}, {"range": "884", "text": "871"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "885", "text": "886"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "887", "text": "888"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "889", "text": "890"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "891", "text": "892"}, {"range": "893", "text": "890"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "894", "text": "895"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "896", "text": "897"}, {"range": "898", "text": "871"}, "Update the dependencies array to be: [images]", {"range": "899", "text": "900"}, {"range": "901", "text": "871"}, {"range": "902", "text": "900"}, "Update the dependencies array to be: [currentYear, loadData]", {"range": "903", "text": "904"}, {"range": "905", "text": "871"}, "Update the dependencies array to be: [currentPage, loadEvents, searchTerm]", {"range": "906", "text": "907"}, "Update the dependencies array to be: [currentPage, loadStudents, searchTerm]", {"range": "908", "text": "909"}, "Update the dependencies array to be: [currentPage, loadAnnouncements, searchTerm]", {"range": "910", "text": "911"}, "Update the dependencies array to be: [isPlaying, currentSlide, createSlideContent]", {"range": "912", "text": "913"}, "Update the dependencies array to be: [handleControlCommand]", {"range": "914", "text": "915"}, "Update the dependencies array to be: [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter, loadAdmins]", {"range": "916", "text": "917"}, "Update the dependencies array to be: [permissions, permissions.canManageCategories, user]", {"range": "918", "text": "919"}, "Update the dependencies array to be: [category, subcategory, mode, isOpen, isEditMode, isSubcategoryMode]", {"range": "920", "text": "921"}, [8001, 8052], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [4681, 4735], "[currentPage, debouncedSearchTerm, filterStatus, permissions.isSuperAdmin, user.grade_level]", [900, 910], "[duration, handleClose]", [1735, 1746], "[imagePath, imageUrl]", [3099, 3126], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636], [1557, 1570], "[currentYear, loadData]", [3031, 3042], [958, 983], "[currentPage, loadEvents, searchTerm]", [938, 963], "[currentPage, loadStudents, searchTerm]", [1032, 1057], "[currentPage, loadAnnouncements, searchTerm]", [5902, 5927], "[isPlaying, currentSlide, createSlideContent]", [6428, 6430], "[handleControlCommand]", [2172, 2270], "[permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter, loadAdmins]", [2395, 2428], "[permissions, permissions.canManageCategories, user]", [2225, 2262], "[category, subcategory, mode, isOpen, isEditMode, isSubcategoryMode]"]